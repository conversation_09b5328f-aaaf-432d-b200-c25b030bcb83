<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版本 - 3D 我的世界风格游戏</title>
    <style>
        body { 
            margin: 0; 
            background: #000; 
            font-family: Arial, sans-serif;
            color: white;
        }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }
        #start-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px 40px;
            font-size: 18px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 200;
        }
        #start-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>状态: <span id="status">初始化中...</span></div>
        <div>Three.js: <span id="threejs-status">检查中...</span></div>
        <div>WebGL: <span id="webgl-status">检查中...</span></div>
    </div>
    
    <button id="start-btn">开始游戏</button>

    <!-- Three.js 本地库 -->
    <script src="libs/three.min.js"></script>
    
    <script>
        let scene, camera, renderer, cube;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('状态:', message);
        }
        
        function checkThreeJS() {
            if (window.THREE) {
                document.getElementById('threejs-status').textContent = `已加载 (r${THREE.REVISION})`;
                document.getElementById('threejs-status').style.color = '#4CAF50';
                return true;
            } else {
                document.getElementById('threejs-status').textContent = '未加载';
                document.getElementById('threejs-status').style.color = '#f44336';
                return false;
            }
        }
        
        function checkWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    document.getElementById('webgl-status').textContent = '支持';
                    document.getElementById('webgl-status').style.color = '#4CAF50';
                    return true;
                } else {
                    document.getElementById('webgl-status').textContent = '不支持';
                    document.getElementById('webgl-status').style.color = '#f44336';
                    return false;
                }
            } catch (e) {
                document.getElementById('webgl-status').textContent = '错误';
                document.getElementById('webgl-status').style.color = '#f44336';
                return false;
            }
        }
        
        function initThreeJS() {
            try {
                updateStatus('创建场景...');
                
                // 创建场景
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);
                
                // 创建相机
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                camera.position.set(0, 5, 10);
                
                // 创建渲染器
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.body.appendChild(renderer.domElement);
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                scene.add(directionalLight);
                
                // 创建一些测试方块
                const geometry = new THREE.BoxGeometry(1, 1, 1);
                
                // 草地方块
                const grassMaterial = new THREE.MeshLambertMaterial({ color: 0x4CAF50 });
                const grassCube = new THREE.Mesh(geometry, grassMaterial);
                grassCube.position.set(0, 0, 0);
                grassCube.castShadow = true;
                grassCube.receiveShadow = true;
                scene.add(grassCube);
                
                // 石头方块
                const stoneMaterial = new THREE.MeshLambertMaterial({ color: 0x757575 });
                const stoneCube = new THREE.Mesh(geometry, stoneMaterial);
                stoneCube.position.set(2, 0, 0);
                stoneCube.castShadow = true;
                stoneCube.receiveShadow = true;
                scene.add(stoneCube);
                
                // 木头方块
                const woodMaterial = new THREE.MeshLambertMaterial({ color: 0x8D6E63 });
                const woodCube = new THREE.Mesh(geometry, woodMaterial);
                woodCube.position.set(-2, 0, 0);
                woodCube.castShadow = true;
                woodCube.receiveShadow = true;
                scene.add(woodCube);
                
                // 创建地面
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x8D6E63 });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -1;
                ground.receiveShadow = true;
                scene.add(ground);
                
                updateStatus('渲染中...');
                
                // 开始渲染循环
                animate();
                
                // 隐藏开始按钮
                document.getElementById('start-btn').style.display = 'none';
                
                updateStatus('游戏运行中');
                
            } catch (error) {
                updateStatus('初始化失败: ' + error.message);
                console.error('Three.js 初始化错误:', error);
            }
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            // 旋转相机
            const time = Date.now() * 0.001;
            camera.position.x = Math.cos(time) * 10;
            camera.position.z = Math.sin(time) * 10;
            camera.lookAt(0, 0, 0);
            
            renderer.render(scene, camera);
        }
        
        // 窗口大小改变处理
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('检查环境...');
            
            setTimeout(() => {
                const threeOK = checkThreeJS();
                const webglOK = checkWebGL();
                
                if (threeOK && webglOK) {
                    updateStatus('环境检查通过，点击开始游戏');
                    document.getElementById('start-btn').addEventListener('click', initThreeJS);
                } else {
                    updateStatus('环境检查失败');
                    document.getElementById('start-btn').textContent = '环境不支持';
                    document.getElementById('start-btn').disabled = true;
                }
            }, 100);
        });
    </script>
</body>
</html>
