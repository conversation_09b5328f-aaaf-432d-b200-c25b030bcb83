// 方块系统

// 方块类型定义
const BLOCK_TYPES = {
    AIR: 0,
    GRASS: 1,
    STONE: 2,
    WOOD: 3,
    DIRT: 4,
    SAND: 5
};

// 方块属性配置
const BLOCK_CONFIG = {
    [BLOCK_TYPES.AIR]: {
        name: 'air',
        displayName: '空气',
        color: 0x000000,
        transparent: true,
        solid: false
    },
    [BLOCK_TYPES.GRASS]: {
        name: 'grass',
        displayName: '草地',
        color: 0x4CAF50,
        transparent: false,
        solid: true,
        topColor: 0x7CB342,
        sideColor: 0x4CAF50,
        bottomColor: 0x8D6E63
    },
    [BLOCK_TYPES.STONE]: {
        name: 'stone',
        displayName: '石头',
        color: 0x757575,
        transparent: false,
        solid: true
    },
    [BLOCK_TYPES.WOOD]: {
        name: 'wood',
        displayName: '木头',
        color: 0x8D6E63,
        transparent: false,
        solid: true,
        topColor: 0x8D6E63,
        sideColor: 0x6D4C41,
        bottomColor: 0x5D4037
    },
    [BLOCK_TYPES.DIRT]: {
        name: 'dirt',
        displayName: '泥土',
        color: 0x8D6E63,
        transparent: false,
        solid: true
    },
    [BLOCK_TYPES.SAND]: {
        name: 'sand',
        displayName: '沙子',
        color: 0xFFC107,
        transparent: false,
        solid: true
    }
};

// 方块管理器
class BlockManager {
    constructor() {
        this.blockGeometry = new THREE.BoxGeometry(1, 1, 1);
        this.materials = this.createMaterials();
        this.meshes = new Map(); // 存储每种方块类型的网格
    }

    // 创建方块材质
    createMaterials() {
        const materials = {};

        Object.keys(BLOCK_CONFIG).forEach(blockType => {
            const config = BLOCK_CONFIG[blockType];
            if (config.name === 'air') return;

            console.log(`创建方块材质: ${config.name} (${blockType}), 颜色: ${config.color.toString(16)}`);

            // 暂时使用单一材质简化调试
            materials[blockType] = new THREE.MeshLambertMaterial({
                color: config.color
            });

            /* 多面材质暂时禁用
            if (config.topColor && config.sideColor && config.bottomColor) {
                materials[blockType] = [
                    new THREE.MeshLambertMaterial({ color: config.sideColor }), // 右
                    new THREE.MeshLambertMaterial({ color: config.sideColor }), // 左
                    new THREE.MeshLambertMaterial({ color: config.topColor }),  // 上
                    new THREE.MeshLambertMaterial({ color: config.bottomColor }), // 下
                    new THREE.MeshLambertMaterial({ color: config.sideColor }), // 前
                    new THREE.MeshLambertMaterial({ color: config.sideColor })  // 后
                ];
            } else {
                materials[blockType] = new THREE.MeshLambertMaterial({
                    color: config.color
                });
            }
            */
        });

        console.log('材质创建完成，总数:', Object.keys(materials).length);
        return materials;
    }

    // 创建方块网格
    createBlockMesh(blockType, position) {
        const config = BLOCK_CONFIG[blockType];
        if (!config || config.name === 'air') return null;

        const material = this.materials[blockType];
        const mesh = new THREE.Mesh(this.blockGeometry, material);

        mesh.position.set(position.x, position.y, position.z);
        mesh.userData = {
            blockType: blockType,
            position: { ...position }
        };

        // 启用阴影
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        return mesh;
    }

    // 获取方块配置
    getBlockConfig(blockType) {
        return BLOCK_CONFIG[blockType] || BLOCK_CONFIG[BLOCK_TYPES.AIR];
    }

    // 检查方块是否为固体
    isSolid(blockType) {
        const config = this.getBlockConfig(blockType);
        return config.solid;
    }

    // 检查方块是否透明
    isTransparent(blockType) {
        const config = this.getBlockConfig(blockType);
        return config.transparent;
    }

    // 根据名称获取方块类型
    getBlockTypeByName(name) {
        for (const [type, config] of Object.entries(BLOCK_CONFIG)) {
            if (config.name === name) {
                return parseInt(type);
            }
        }
        return BLOCK_TYPES.AIR;
    }

    // 获取所有可用的方块类型（排除空气）
    getAvailableBlockTypes() {
        return Object.keys(BLOCK_CONFIG)
            .map(type => parseInt(type))
            .filter(type => type !== BLOCK_TYPES.AIR);
    }

    // 创建方块实例化网格（用于性能优化）
    createInstancedMesh(blockType, maxCount = 1000) {
        const config = BLOCK_CONFIG[blockType];
        if (!config || config.name === 'air') {
            console.log(`跳过创建实例化网格: ${blockType} (空气或无效)`);
            return null;
        }

        const material = this.materials[blockType];
        if (!material) {
            console.error(`材质未找到: ${blockType}`);
            return null;
        }

        console.log(`创建实例化网格: ${config.name} (${blockType}), 最大数量: ${maxCount}`);

        const instancedMesh = new THREE.InstancedMesh(
            this.blockGeometry,
            material,
            maxCount
        );

        instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
        instancedMesh.castShadow = true;
        instancedMesh.receiveShadow = true;

        console.log(`实例化网格创建成功: ${config.name}`);
        return instancedMesh;
    }

    // 更新实例化网格中的方块位置
    updateInstancedBlock(instancedMesh, index, position, visible = true) {
        const matrix = new THREE.Matrix4();
        matrix.setPosition(position.x, position.y, position.z);
        instancedMesh.setMatrixAt(index, matrix);

        if (!visible) {
            // 隐藏方块（移动到远处）
            matrix.setPosition(999999, 999999, 999999);
            instancedMesh.setMatrixAt(index, matrix);
        }

        instancedMesh.instanceMatrix.needsUpdate = true;
    }
}

// 方块选择器（用于UI）
class BlockSelector {
    constructor(blockManager) {
        this.blockManager = blockManager;
        this.selectedBlockType = BLOCK_TYPES.GRASS;
        this.availableBlocks = blockManager.getAvailableBlockTypes();
        this.currentIndex = 0;
    }

    // 选择方块类型
    selectBlock(blockType) {
        if (this.availableBlocks.includes(blockType)) {
            this.selectedBlockType = blockType;
            this.currentIndex = this.availableBlocks.indexOf(blockType);
            return true;
        }
        return false;
    }

    // 选择下一个方块
    selectNext() {
        this.currentIndex = (this.currentIndex + 1) % this.availableBlocks.length;
        this.selectedBlockType = this.availableBlocks[this.currentIndex];
        return this.selectedBlockType;
    }

    // 选择上一个方块
    selectPrevious() {
        this.currentIndex = (this.currentIndex - 1 + this.availableBlocks.length) % this.availableBlocks.length;
        this.selectedBlockType = this.availableBlocks[this.currentIndex];
        return this.selectedBlockType;
    }

    // 根据索引选择方块
    selectByIndex(index) {
        if (index >= 0 && index < this.availableBlocks.length) {
            this.currentIndex = index;
            this.selectedBlockType = this.availableBlocks[index];
            return this.selectedBlockType;
        }
        return this.selectedBlockType;
    }

    // 获取当前选中的方块类型
    getSelectedBlock() {
        return this.selectedBlockType;
    }

    // 获取当前选中方块的配置
    getSelectedBlockConfig() {
        return this.blockManager.getBlockConfig(this.selectedBlockType);
    }
}

// 导出到全局
window.BLOCK_TYPES = BLOCK_TYPES;
window.BLOCK_CONFIG = BLOCK_CONFIG;
window.BlockManager = BlockManager;
window.BlockSelector = BlockSelector;
