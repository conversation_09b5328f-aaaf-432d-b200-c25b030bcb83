// 主游戏逻辑

// 游戏主类
class MinecraftGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.world = null;
        this.player = null;
        this.blockManager = null;
        this.blockSelector = null;
        this.ui = null;

        // 游戏状态
        this.isRunning = false;
        this.isPaused = false;
        this.lastTime = 0;

        // 性能监控
        this.performanceMonitor = new GameUtils.PerformanceMonitor();

        // 事件管理器
        this.eventManager = new GameUtils.EventManager();

        this.init();
    }

    // 初始化游戏
    init() {
        try {
            this.initThreeJS();
            this.initGameSystems();
            this.initUI();
            this.setupEventListeners();

            console.log('游戏初始化完成');
        } catch (error) {
            console.error('游戏初始化失败:', error);
            this.showError('游戏初始化失败: ' + error.message);
        }
    }

    // 初始化Three.js
    initThreeJS() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // 天空蓝色

        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75, // 视野角度
            window.innerWidth / window.innerHeight, // 宽高比
            0.1, // 近裁剪面
            1000 // 远裁剪面
        );

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('game-canvas'),
            antialias: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // 添加光照
        this.setupLighting();

        console.log('Three.js 初始化完成');
    }

    // 设置光照
    setupLighting() {
        // 环境光 - 增加亮度
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        console.log('添加环境光');

        // 方向光（太阳光）- 增加亮度
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;

        // 设置阴影参数
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;

        this.scene.add(directionalLight);
        console.log('添加方向光');

        // 添加额外的点光源确保有足够的光照
        const pointLight = new THREE.PointLight(0xffffff, 0.5, 100);
        pointLight.position.set(0, 50, 0);
        this.scene.add(pointLight);
        console.log('添加点光源');

        // 暂时移除雾效，可能影响可见性
        // this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);

        // 添加多个测试立方体确保渲染工作
        const testGeometry = new THREE.BoxGeometry(2, 2, 2);

        // 红色测试立方体
        const testMaterial1 = new THREE.MeshLambertMaterial({ color: 0xff0000 });
        const testCube1 = new THREE.Mesh(testGeometry, testMaterial1);
        testCube1.position.set(0, 40, -5);
        this.scene.add(testCube1);

        // 绿色测试立方体
        const testMaterial2 = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
        const testCube2 = new THREE.Mesh(testGeometry, testMaterial2);
        testCube2.position.set(3, 40, -5);
        this.scene.add(testCube2);

        // 蓝色测试立方体
        const testMaterial3 = new THREE.MeshLambertMaterial({ color: 0x0000ff });
        const testCube3 = new THREE.Mesh(testGeometry, testMaterial3);
        testCube3.position.set(-3, 40, -5);
        this.scene.add(testCube3);

        console.log('添加测试立方体到场景');
    }

    // 初始化游戏系统
    initGameSystems() {
        // 初始化方块管理器
        this.blockManager = new BlockManager();
        console.log('方块管理器初始化完成');

        // 初始化世界
        this.world = new World(this.scene, this.blockManager);
        console.log('世界管理器初始化完成');

        // 初始化方块选择器
        this.blockSelector = new BlockSelector(this.blockManager);
        console.log('方块选择器初始化完成');

        // 初始化玩家
        console.log('开始初始化玩家...');
        console.log('相机对象:', this.camera);
        console.log('世界对象:', this.world);
        console.log('场景对象:', this.scene);
        this.player = new Player(this.camera, this.world, this.scene);
        console.log('玩家初始化完成');

        // 预生成原点区块以获取地面高度
        this.world.getChunk(0, 0);
        const groundHeight = this.world.getGroundHeight(0, 0);
        console.log('地面高度:', groundHeight);

        // 设置一个固定的安全位置
        const safeY = Math.max(groundHeight + 2, 40);
        this.player.setPosition(0, safeY, 0);
        console.log('玩家位置设置为:', this.player.getPosition());

        // 第三人称相机会自动设置位置和朝向，不需要手动设置
        console.log('第三人称相机初始化完成');

        console.log('游戏系统初始化完成');
    }

    // 初始化UI
    initUI() {
        this.ui = new UIManager(this.blockSelector);
        console.log('UI 初始化完成');
    }

    // 设置事件监听器
    setupEventListeners() {
        // 窗口大小改变
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });

        // 页面可见性改变
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });

        // 错误处理
        window.addEventListener('error', (event) => {
            console.error('游戏运行错误:', event.error);
            this.showError('游戏运行错误: ' + event.error.message);
        });
    }

    // 开始游戏
    start() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.isPaused = false;
        this.lastTime = performance.now();

        // 初始化游戏界面
        this.ui.initGameUI();

        // 强制加载初始区块
        console.log('加载初始区块...');
        for (let x = -2; x <= 2; x++) {
            for (let z = -2; z <= 2; z++) {
                this.world.loadChunk(x, z);
            }
        }
        console.log('初始区块加载完成，已加载区块数:', this.world.loadedChunks.size);

        // 添加一些测试方块确保渲染工作
        console.log('添加测试方块...');
        try {
            // 使用世界系统添加方块
            this.world.setBlock(0, 35, 0, BLOCK_TYPES.GRASS);
            this.world.setBlock(1, 35, 0, BLOCK_TYPES.STONE);
            this.world.setBlock(0, 35, 1, BLOCK_TYPES.WOOD);
            this.world.setBlock(-1, 35, 0, BLOCK_TYPES.DIRT);
            this.world.setBlock(0, 35, -1, BLOCK_TYPES.SAND);

            // 直接添加一些简单的方块到场景中作为备用
            const geometry = new THREE.BoxGeometry(1, 1, 1);

            // 直接创建方块网格
            const materials = [
                new THREE.MeshLambertMaterial({ color: 0x4CAF50 }), // 草地
                new THREE.MeshLambertMaterial({ color: 0x757575 }), // 石头
                new THREE.MeshLambertMaterial({ color: 0x8D6E63 }), // 木头
            ];

            for (let i = 0; i < 3; i++) {
                const mesh = new THREE.Mesh(geometry, materials[i]);
                mesh.position.set(i * 2 - 2, 38, -3);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                this.scene.add(mesh);
                console.log(`直接添加方块到场景: ${i}`);
            }

            console.log('测试方块添加完成');
        } catch (error) {
            console.error('添加测试方块时出错:', error);
        }

        // 开始游戏循环
        this.gameLoop();

        console.log('游戏开始');
        this.ui.showSuccess('游戏开始！');
    }

    // 暂停游戏
    pause() {
        this.isPaused = true;
        console.log('游戏暂停');
    }

    // 恢复游戏
    resume() {
        if (this.isRunning && this.isPaused) {
            this.isPaused = false;
            this.lastTime = performance.now();
            console.log('游戏恢复');
        }
    }

    // 停止游戏
    stop() {
        this.isRunning = false;
        this.isPaused = false;
        console.log('游戏停止');
    }

    // 游戏主循环
    gameLoop() {
        if (!this.isRunning) return;

        requestAnimationFrame(() => this.gameLoop());

        if (this.isPaused) return;

        const currentTime = performance.now();
        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;

        // 限制deltaTime防止大跳跃
        const clampedDeltaTime = Math.min(deltaTime, 1/30);

        this.update(clampedDeltaTime);
        this.render();

        // 更新性能监控
        this.performanceMonitor.update();

        // 更新调试信息
        this.updateDebugInfo();
    }

    // 更新游戏状态
    update(deltaTime) {
        // 更新玩家
        this.player.update(deltaTime);

        // 触发更新事件
        this.eventManager.emit('update', deltaTime);
    }

    // 渲染游戏
    render() {
        this.renderer.render(this.scene, this.camera);

        // 触发渲染事件
        this.eventManager.emit('render');

        // 每隔一段时间输出场景信息
        if (this.frameCount % 300 === 0) { // 每5秒左右
            console.log('场景对象数量:', this.scene.children.length);
            console.log('相机位置:', this.camera.position);
            console.log('相机旋转:', this.camera.rotation);
        }

        this.frameCount = (this.frameCount || 0) + 1;
    }

    // 窗口大小改变处理
    onWindowResize() {
        const width = window.innerWidth;
        const height = window.innerHeight;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }

    // 更新调试信息
    updateDebugInfo() {
        const playerPos = this.player.getPosition();
        const chunkCoord = this.world.getChunkCoord(playerPos.x, playerPos.z);
        const selectedBlockConfig = this.blockSelector.getSelectedBlockConfig();

        const debugInfo = {
            fps: this.performanceMonitor.getFPS(),
            position: playerPos,
            chunk: `${chunkCoord.x}, ${chunkCoord.z}`,
            selectedBlock: selectedBlockConfig.displayName,
            loadedChunks: this.world.loadedChunks.size
        };

        this.ui.updateDebugInfo(debugInfo);
    }

    // 显示错误
    showError(message) {
        if (this.ui) {
            this.ui.showError(message);
        } else {
            alert(message);
        }
    }

    // 重置游戏
    reset() {
        this.stop();

        // 清理场景
        while(this.scene.children.length > 0) {
            this.scene.remove(this.scene.children[0]);
        }

        // 重新初始化
        this.init();

        console.log('游戏重置完成');
    }

    // 销毁游戏
    destroy() {
        this.stop();

        // 清理资源
        if (this.renderer) {
            this.renderer.dispose();
        }

        // 移除事件监听器
        window.removeEventListener('resize', this.onWindowResize);
        document.removeEventListener('visibilitychange', this.pause);

        console.log('游戏销毁完成');
    }
}

// 游戏初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面加载完成，初始化游戏...');

    // 等待一下确保Three.js完全加载
    setTimeout(() => {
        // 检查浏览器支持
        if (!window.THREE) {
            console.error('Three.js 未加载');
            alert('无法加载 Three.js 库，请检查文件是否存在');
            return;
        }

        console.log('Three.js 版本:', THREE.REVISION);

        if (!window.WebGLRenderingContext) {
            alert('您的浏览器不支持 WebGL，无法运行游戏');
            return;
        }

        // 创建游戏实例
        try {
            window.gameInstance = new MinecraftGame();
            console.log('游戏实例创建成功');
        } catch (error) {
            console.error('游戏初始化失败:', error);
            alert('游戏初始化失败: ' + error.message);
        }

        // 隐藏加载界面
        setTimeout(() => {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.classList.add('hidden');
            }
        }, 1000);
    }, 100);
});

// 导出到全局
window.MinecraftGame = MinecraftGame;
