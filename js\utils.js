// 工具函数集合

// 3D坐标转换为世界坐标键
function coordToKey(x, y, z) {
    return `${Math.floor(x)},${Math.floor(y)},${Math.floor(z)}`;
}

// 世界坐标键转换为3D坐标
function keyToCoord(key) {
    const parts = key.split(',');
    return {
        x: parseInt(parts[0]),
        y: parseInt(parts[1]),
        z: parseInt(parts[2])
    };
}

// 生成随机整数
function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成随机浮点数
function randomFloat(min, max) {
    return Math.random() * (max - min) + min;
}

// 简单的噪声函数（用于地形生成）
function simpleNoise(x, z, scale = 0.1) {
    const noise = Math.sin(x * scale) * Math.cos(z * scale) * 0.5 + 
                  Math.sin(x * scale * 2) * Math.cos(z * scale * 2) * 0.25 +
                  Math.sin(x * scale * 4) * Math.cos(z * scale * 4) * 0.125;
    return noise;
}

// 限制数值在指定范围内
function clamp(value, min, max) {
    return Math.max(min, Math.min(max, value));
}

// 线性插值
function lerp(start, end, factor) {
    return start + (end - start) * factor;
}

// 检查两个AABB包围盒是否相交
function aabbIntersect(box1, box2) {
    return (box1.min.x <= box2.max.x && box1.max.x >= box2.min.x) &&
           (box1.min.y <= box2.max.y && box1.max.y >= box2.min.y) &&
           (box1.min.z <= box2.max.z && box1.max.z >= box2.min.z);
}

// 创建AABB包围盒
function createAABB(center, size) {
    const halfSize = size / 2;
    return {
        min: {
            x: center.x - halfSize,
            y: center.y - halfSize,
            z: center.z - halfSize
        },
        max: {
            x: center.x + halfSize,
            y: center.y + halfSize,
            z: center.z + halfSize
        }
    };
}

// 向量运算工具
const VectorUtils = {
    // 向量加法
    add: (v1, v2) => ({
        x: v1.x + v2.x,
        y: v1.y + v2.y,
        z: v1.z + v2.z
    }),
    
    // 向量减法
    subtract: (v1, v2) => ({
        x: v1.x - v2.x,
        y: v1.y - v2.y,
        z: v1.z - v2.z
    }),
    
    // 向量标量乘法
    multiply: (v, scalar) => ({
        x: v.x * scalar,
        y: v.y * scalar,
        z: v.z * scalar
    }),
    
    // 向量长度
    length: (v) => Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z),
    
    // 向量归一化
    normalize: (v) => {
        const len = VectorUtils.length(v);
        if (len === 0) return { x: 0, y: 0, z: 0 };
        return VectorUtils.multiply(v, 1 / len);
    },
    
    // 向量点积
    dot: (v1, v2) => v1.x * v2.x + v1.y * v2.y + v1.z * v2.z,
    
    // 向量叉积
    cross: (v1, v2) => ({
        x: v1.y * v2.z - v1.z * v2.y,
        y: v1.z * v2.x - v1.x * v2.z,
        z: v1.x * v2.y - v1.y * v2.x
    })
};

// 性能监控工具
class PerformanceMonitor {
    constructor() {
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
    }
    
    update() {
        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }
    
    getFPS() {
        return this.fps;
    }
}

// 事件管理器
class EventManager {
    constructor() {
        this.events = {};
    }
    
    on(eventName, callback) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(callback);
    }
    
    off(eventName, callback) {
        if (!this.events[eventName]) return;
        
        const index = this.events[eventName].indexOf(callback);
        if (index > -1) {
            this.events[eventName].splice(index, 1);
        }
    }
    
    emit(eventName, ...args) {
        if (!this.events[eventName]) return;
        
        this.events[eventName].forEach(callback => {
            callback(...args);
        });
    }
}

// 导出全局变量
window.GameUtils = {
    coordToKey,
    keyToCoord,
    randomInt,
    randomFloat,
    simpleNoise,
    clamp,
    lerp,
    aabbIntersect,
    createAABB,
    VectorUtils,
    PerformanceMonitor,
    EventManager
};
