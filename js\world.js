// 世界生成和管理系统

// 世界配置
const WORLD_CONFIG = {
    CHUNK_SIZE: 16,        // 区块大小
    WORLD_HEIGHT: 64,      // 世界高度
    SEA_LEVEL: 32,         // 海平面高度
    RENDER_DISTANCE: 3,    // 渲染距离（区块数）
    TERRAIN_SCALE: 0.05,   // 地形噪声缩放
    TERRAIN_HEIGHT: 10     // 地形高度变化
};

// 区块类
class Chunk {
    constructor(x, z, world) {
        this.x = x;
        this.z = z;
        this.world = world;
        this.blocks = new Map(); // 存储方块数据
        this.meshes = new Map(); // 存储渲染网格
        this.generated = false;
        this.loaded = false;

        this.generate();
    }

    // 生成区块地形
    generate() {
        const startX = this.x * WORLD_CONFIG.CHUNK_SIZE;
        const startZ = this.z * WORLD_CONFIG.CHUNK_SIZE;

        console.log(`生成区块 (${this.x}, ${this.z}), 世界坐标: (${startX}, ${startZ})`);

        for (let x = 0; x < WORLD_CONFIG.CHUNK_SIZE; x++) {
            for (let z = 0; z < WORLD_CONFIG.CHUNK_SIZE; z++) {
                const worldX = startX + x;
                const worldZ = startZ + z;

                // 简化地形生成 - 创建一个平坦的基础层
                const baseHeight = 30;
                const height = baseHeight + Math.floor(GameUtils.simpleNoise(worldX, worldZ, 0.1) * 5);

                // 生成地形层
                for (let y = 0; y <= height; y++) {
                    let blockType;

                    if (y === height) {
                        blockType = BLOCK_TYPES.GRASS; // 草地表面
                    } else if (y > height - 3) {
                        blockType = BLOCK_TYPES.DIRT; // 泥土层
                    } else {
                        blockType = BLOCK_TYPES.STONE; // 石头层
                    }

                    this.setBlock(worldX, y, worldZ, blockType);
                }

                // 在某些位置生成木头（简单的树）
                if (Math.random() < 0.01) {
                    const treeHeight = GameUtils.randomInt(2, 4);
                    for (let y = height + 1; y <= height + treeHeight; y++) {
                        this.setBlock(worldX, y, worldZ, BLOCK_TYPES.WOOD);
                    }
                }
            }
        }

        console.log(`区块 (${this.x}, ${this.z}) 生成完成，方块数量:`, this.blocks.size);
        this.generated = true;
    }

    // 生成地形高度
    generateHeight(x, z) {
        // 简化地形生成，确保有可见的地面
        const noise1 = GameUtils.simpleNoise(x, z, WORLD_CONFIG.TERRAIN_SCALE);
        const noise2 = GameUtils.simpleNoise(x, z, WORLD_CONFIG.TERRAIN_SCALE * 2) * 0.5;

        const combinedNoise = noise1 + noise2;
        const height = WORLD_CONFIG.SEA_LEVEL + Math.floor(combinedNoise * WORLD_CONFIG.TERRAIN_HEIGHT);

        return GameUtils.clamp(height, 20, WORLD_CONFIG.WORLD_HEIGHT - 10);
    }

    // 设置方块
    setBlock(x, y, z, blockType) {
        const key = GameUtils.coordToKey(x, y, z);
        this.blocks.set(key, blockType);
    }

    // 获取方块
    getBlock(x, y, z) {
        const key = GameUtils.coordToKey(x, y, z);
        return this.blocks.get(key) || BLOCK_TYPES.AIR;
    }

    // 移除方块
    removeBlock(x, y, z) {
        const key = GameUtils.coordToKey(x, y, z);
        this.blocks.delete(key);
    }

    // 检查方块是否存在
    hasBlock(x, y, z) {
        const key = GameUtils.coordToKey(x, y, z);
        return this.blocks.has(key);
    }

    // 生成网格
    generateMesh(scene, blockManager) {
        console.log(`为区块 (${this.x}, ${this.z}) 生成网格，方块总数:`, this.blocks.size);

        // 清除现有网格
        this.clearMesh(scene);

        // 为每种方块类型创建网格
        const blockCounts = new Map();
        const blockPositions = new Map();

        // 统计每种方块类型的数量和位置
        this.blocks.forEach((blockType, key) => {
            if (blockType === BLOCK_TYPES.AIR) return;

            const coord = GameUtils.keyToCoord(key);

            // 检查是否需要渲染（面剔除优化）
            if (this.shouldRenderBlock(coord.x, coord.y, coord.z)) {
                if (!blockCounts.has(blockType)) {
                    blockCounts.set(blockType, 0);
                    blockPositions.set(blockType, []);
                }

                blockCounts.set(blockType, blockCounts.get(blockType) + 1);
                blockPositions.get(blockType).push(coord);
            }
        });

        console.log('方块统计:', Array.from(blockCounts.entries()).map(([type, count]) => `${type}: ${count}`));

        // 为每种方块类型创建实例化网格
        blockCounts.forEach((count, blockType) => {
            console.log(`创建方块类型 ${blockType} 的实例化网格，数量: ${count}`);

            const instancedMesh = blockManager.createInstancedMesh(blockType, count);
            if (!instancedMesh) {
                console.error(`无法创建方块类型 ${blockType} 的实例化网格`);
                return;
            }

            const positions = blockPositions.get(blockType);
            positions.forEach((pos, index) => {
                blockManager.updateInstancedBlock(instancedMesh, index, pos, true);
            });

            scene.add(instancedMesh);
            this.meshes.set(blockType, instancedMesh);
            console.log(`方块类型 ${blockType} 的网格已添加到场景`);
        });

        console.log(`区块 (${this.x}, ${this.z}) 网格生成完成，网格数量:`, this.meshes.size);
        this.loaded = true;
    }

    // 检查方块是否需要渲染（面剔除）
    shouldRenderBlock(x, y, z) {
        // 暂时禁用面剔除，确保所有方块都能渲染
        return true;

        // TODO: 后续可以重新启用面剔除优化
        /*
        const directions = [
            { x: 1, y: 0, z: 0 },   // 右
            { x: -1, y: 0, z: 0 },  // 左
            { x: 0, y: 1, z: 0 },   // 上
            { x: 0, y: -1, z: 0 },  // 下
            { x: 0, y: 0, z: 1 },   // 前
            { x: 0, y: 0, z: -1 }   // 后
        ];

        for (const dir of directions) {
            const neighborX = x + dir.x;
            const neighborY = y + dir.y;
            const neighborZ = z + dir.z;

            const neighborBlock = this.world.getBlock(neighborX, neighborY, neighborZ);

            if (neighborBlock === BLOCK_TYPES.AIR ||
                this.world.blockManager.isTransparent(neighborBlock)) {
                return true;
            }
        }

        return false;
        */
    }

    // 清除网格
    clearMesh(scene) {
        this.meshes.forEach(mesh => {
            scene.remove(mesh);
            mesh.dispose();
        });
        this.meshes.clear();
        this.loaded = false;
    }

    // 更新网格
    updateMesh(scene, blockManager) {
        if (this.loaded) {
            this.generateMesh(scene, blockManager);
        }
    }
}

// 世界管理器
class World {
    constructor(scene, blockManager) {
        this.scene = scene;
        this.blockManager = blockManager;
        this.chunks = new Map();
        this.loadedChunks = new Set();
        this.playerChunkX = 0;
        this.playerChunkZ = 0;
    }

    // 获取区块坐标
    getChunkCoord(x, z) {
        return {
            x: Math.floor(x / WORLD_CONFIG.CHUNK_SIZE),
            z: Math.floor(z / WORLD_CONFIG.CHUNK_SIZE)
        };
    }

    // 获取区块键
    getChunkKey(chunkX, chunkZ) {
        return `${chunkX},${chunkZ}`;
    }

    // 获取或创建区块
    getChunk(chunkX, chunkZ) {
        const key = this.getChunkKey(chunkX, chunkZ);

        if (!this.chunks.has(key)) {
            const chunk = new Chunk(chunkX, chunkZ, this);
            this.chunks.set(key, chunk);
        }

        return this.chunks.get(key);
    }

    // 加载区块
    loadChunk(chunkX, chunkZ) {
        const chunk = this.getChunk(chunkX, chunkZ);
        const key = this.getChunkKey(chunkX, chunkZ);

        if (!this.loadedChunks.has(key)) {
            chunk.generateMesh(this.scene, this.blockManager);
            this.loadedChunks.add(key);
        }

        return chunk;
    }

    // 卸载区块
    unloadChunk(chunkX, chunkZ) {
        const key = this.getChunkKey(chunkX, chunkZ);
        const chunk = this.chunks.get(key);

        if (chunk && this.loadedChunks.has(key)) {
            chunk.clearMesh(this.scene);
            this.loadedChunks.delete(key);
        }
    }

    // 更新玩家周围的区块
    updateChunks(playerX, playerZ) {
        const playerChunk = this.getChunkCoord(playerX, playerZ);

        // 如果玩家移动到新区块，更新加载的区块
        if (playerChunk.x !== this.playerChunkX || playerChunk.z !== this.playerChunkZ) {
            this.playerChunkX = playerChunk.x;
            this.playerChunkZ = playerChunk.z;

            // 加载玩家周围的区块
            const chunksToLoad = new Set();
            for (let x = -WORLD_CONFIG.RENDER_DISTANCE; x <= WORLD_CONFIG.RENDER_DISTANCE; x++) {
                for (let z = -WORLD_CONFIG.RENDER_DISTANCE; z <= WORLD_CONFIG.RENDER_DISTANCE; z++) {
                    const chunkX = playerChunk.x + x;
                    const chunkZ = playerChunk.z + z;
                    const key = this.getChunkKey(chunkX, chunkZ);
                    chunksToLoad.add(key);

                    // 加载区块
                    this.loadChunk(chunkX, chunkZ);
                }
            }

            // 卸载远离玩家的区块
            const chunksToUnload = [];
            this.loadedChunks.forEach(key => {
                if (!chunksToLoad.has(key)) {
                    chunksToUnload.push(key);
                }
            });

            chunksToUnload.forEach(key => {
                const coord = key.split(',');
                this.unloadChunk(parseInt(coord[0]), parseInt(coord[1]));
            });
        }
    }

    // 获取指定位置的方块
    getBlock(x, y, z) {
        const chunkCoord = this.getChunkCoord(x, z);
        const chunk = this.chunks.get(this.getChunkKey(chunkCoord.x, chunkCoord.z));

        if (chunk) {
            return chunk.getBlock(x, y, z);
        }

        return BLOCK_TYPES.AIR;
    }

    // 设置指定位置的方块
    setBlock(x, y, z, blockType) {
        const chunkCoord = this.getChunkCoord(x, z);
        const chunk = this.getChunk(chunkCoord.x, chunkCoord.z);

        if (blockType === BLOCK_TYPES.AIR) {
            chunk.removeBlock(x, y, z);
        } else {
            chunk.setBlock(x, y, z, blockType);
        }

        // 更新区块网格
        chunk.updateMesh(this.scene, this.blockManager);

        // 如果方块在区块边界，也需要更新相邻区块
        this.updateNeighborChunks(x, z, chunkCoord);
    }

    // 更新相邻区块
    updateNeighborChunks(x, z, chunkCoord) {
        const localX = x - chunkCoord.x * WORLD_CONFIG.CHUNK_SIZE;
        const localZ = z - chunkCoord.z * WORLD_CONFIG.CHUNK_SIZE;

        // 检查是否在区块边界
        const neighbors = [];

        if (localX === 0) neighbors.push({ x: chunkCoord.x - 1, z: chunkCoord.z });
        if (localX === WORLD_CONFIG.CHUNK_SIZE - 1) neighbors.push({ x: chunkCoord.x + 1, z: chunkCoord.z });
        if (localZ === 0) neighbors.push({ x: chunkCoord.x, z: chunkCoord.z - 1 });
        if (localZ === WORLD_CONFIG.CHUNK_SIZE - 1) neighbors.push({ x: chunkCoord.x, z: chunkCoord.z + 1 });

        // 更新相邻区块网格
        neighbors.forEach(neighbor => {
            const key = this.getChunkKey(neighbor.x, neighbor.z);
            const chunk = this.chunks.get(key);
            if (chunk && this.loadedChunks.has(key)) {
                chunk.updateMesh(this.scene, this.blockManager);
            }
        });
    }

    // 检查指定位置是否有固体方块
    isSolid(x, y, z) {
        const blockType = this.getBlock(x, y, z);
        return this.blockManager.isSolid(blockType);
    }

    // 获取地面高度
    getGroundHeight(x, z) {
        for (let y = WORLD_CONFIG.WORLD_HEIGHT - 1; y >= 0; y--) {
            if (this.isSolid(x, y, z)) {
                return y + 1;
            }
        }
        return 0;
    }
}

// 导出到全局
window.WORLD_CONFIG = WORLD_CONFIG;
window.Chunk = Chunk;
window.World = World;
