<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D 我的世界风格游戏</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="game-container">
        <!-- 游戏画布 -->
        <canvas id="game-canvas"></canvas>

        <!-- 十字准星 -->
        <div id="crosshair">
            <div class="crosshair-line horizontal"></div>
            <div class="crosshair-line vertical"></div>
        </div>

        <!-- 工具栏 -->
        <div id="toolbar">
            <div class="toolbar-slot active" data-block="grass">
                <div class="block-icon grass-icon"></div>
                <span>草地</span>
            </div>
            <div class="toolbar-slot" data-block="stone">
                <div class="block-icon stone-icon"></div>
                <span>石头</span>
            </div>
            <div class="toolbar-slot" data-block="wood">
                <div class="block-icon wood-icon"></div>
                <span>木头</span>
            </div>
            <div class="toolbar-slot" data-block="dirt">
                <div class="block-icon dirt-icon"></div>
                <span>泥土</span>
            </div>
            <div class="toolbar-slot" data-block="sand">
                <div class="block-icon sand-icon"></div>
                <span>沙子</span>
            </div>
        </div>

        <!-- 控制说明 -->
        <div id="controls-info">
            <h3>游戏控制（第三人称）</h3>
            <p><strong>WASD</strong> - 移动角色</p>
            <p><strong>鼠标</strong> - 控制相机视角</p>
            <p><strong>鼠标滚轮</strong> - 调整相机距离</p>
            <p><strong>左键</strong> - 破坏方块</p>
            <p><strong>右键</strong> - 放置方块</p>
            <p><strong>数字键1-5</strong> - 选择方块</p>
            <p><strong>Shift</strong> - 奔跑</p>
            <p><strong>空格</strong> - 跳跃</p>
            <p><strong>ESC</strong> - 释放鼠标锁定</p>
            <button id="start-game">开始游戏</button>
        </div>

        <!-- 加载提示 -->
        <div id="loading">
            <div class="loading-spinner"></div>
            <p>正在加载游戏...</p>
        </div>
    </div>

    <!-- Three.js 本地库 -->
    <script src="libs/three.min.js"></script>

    <!-- 游戏脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/blocks.js"></script>
    <script src="js/world.js"></script>
    <script src="js/player.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
