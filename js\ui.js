// 用户界面管理

// UI管理器
class UIManager {
    constructor(blockSelector) {
        this.blockSelector = blockSelector;
        this.elements = {
            loading: document.getElementById('loading'),
            controlsInfo: document.getElementById('controls-info'),
            startButton: document.getElementById('start-game'),
            toolbar: document.getElementById('toolbar'),
            crosshair: document.getElementById('crosshair')
        };

        this.initEventListeners();
    }

    // 初始化事件监听器
    initEventListeners() {
        // 开始游戏按钮
        this.elements.startButton.addEventListener('click', () => {
            this.startGame();
        });

        // 工具栏点击事件
        this.elements.toolbar.addEventListener('click', (event) => {
            const slot = event.target.closest('.toolbar-slot');
            if (slot) {
                const blockType = slot.dataset.block;
                this.selectBlock(blockType);
            }
        });

        // 右键菜单禁用
        document.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });

        // 窗口大小改变事件
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
    }

    // 开始游戏
    startGame() {
        console.log('UI: 开始游戏按钮被点击');
        if (window.gameInstance) {
            console.log('UI: 游戏实例存在，开始启动游戏');
            this.hideLoading();
            this.hideControlsInfo();
            window.gameInstance.start();
        } else {
            console.error('UI: 游戏实例不存在！');
            this.showError('游戏实例未初始化');
        }
    }

    // 选择方块
    selectBlock(blockName) {
        const blockType = this.blockSelector.blockManager.getBlockTypeByName(blockName);
        if (this.blockSelector.selectBlock(blockType)) {
            this.updateToolbar();
        }
    }

    // 更新工具栏
    updateToolbar() {
        const slots = this.elements.toolbar.querySelectorAll('.toolbar-slot');
        const selectedBlock = this.blockSelector.getSelectedBlock();
        const selectedConfig = this.blockSelector.getSelectedBlockConfig();

        slots.forEach(slot => {
            const blockName = slot.dataset.block;
            const blockType = this.blockSelector.blockManager.getBlockTypeByName(blockName);

            if (blockType === selectedBlock) {
                slot.classList.add('active');
            } else {
                slot.classList.remove('active');
            }
        });
    }

    // 显示加载界面
    showLoading() {
        this.elements.loading.classList.remove('hidden');
    }

    // 隐藏加载界面
    hideLoading() {
        this.elements.loading.classList.add('hidden');
    }

    // 显示控制说明
    showControlsInfo() {
        this.elements.controlsInfo.classList.remove('hidden');
    }

    // 隐藏控制说明
    hideControlsInfo() {
        this.elements.controlsInfo.classList.add('hidden');
    }

    // 显示工具栏
    showToolbar() {
        this.elements.toolbar.classList.remove('hidden');
    }

    // 隐藏工具栏
    hideToolbar() {
        this.elements.toolbar.classList.add('hidden');
    }

    // 显示十字准星
    showCrosshair() {
        this.elements.crosshair.classList.remove('hidden');
    }

    // 隐藏十字准星
    hideCrosshair() {
        this.elements.crosshair.classList.add('hidden');
    }

    // 窗口大小改变处理
    onWindowResize() {
        if (window.gameInstance && window.gameInstance.renderer) {
            const width = window.innerWidth;
            const height = window.innerHeight;

            window.gameInstance.renderer.setSize(width, height);
            window.gameInstance.camera.aspect = width / height;
            window.gameInstance.camera.updateProjectionMatrix();
        }
    }

    // 显示错误消息
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 5px;
            z-index: 1000;
            font-size: 16px;
            max-width: 400px;
            text-align: center;
        `;
        errorDiv.textContent = message;

        document.body.appendChild(errorDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 3000);
    }

    // 显示成功消息
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 255, 0, 0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 5px;
            z-index: 1000;
            font-size: 16px;
            max-width: 400px;
            text-align: center;
        `;
        successDiv.textContent = message;

        document.body.appendChild(successDiv);

        // 2秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 2000);
    }

    // 显示信息消息
    showInfo(message) {
        const infoDiv = document.createElement('div');
        infoDiv.className = 'info-message';
        infoDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 255, 0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 5px;
            z-index: 1000;
            font-size: 16px;
            max-width: 400px;
            text-align: center;
        `;
        infoDiv.textContent = message;

        document.body.appendChild(infoDiv);

        // 2秒后自动移除
        setTimeout(() => {
            if (infoDiv.parentNode) {
                infoDiv.parentNode.removeChild(infoDiv);
            }
        }, 2000);
    }

    // 创建调试信息面板
    createDebugPanel() {
        const debugPanel = document.createElement('div');
        debugPanel.id = 'debug-panel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 500;
            min-width: 200px;
        `;

        document.body.appendChild(debugPanel);
        return debugPanel;
    }

    // 更新调试信息
    updateDebugInfo(info) {
        let debugPanel = document.getElementById('debug-panel');
        if (!debugPanel) {
            debugPanel = this.createDebugPanel();
        }

        debugPanel.innerHTML = `
            <div><strong>调试信息</strong></div>
            <div>FPS: ${info.fps || 0}</div>
            <div>位置: ${info.position ? `${info.position.x.toFixed(1)}, ${info.position.y.toFixed(1)}, ${info.position.z.toFixed(1)}` : 'N/A'}</div>
            <div>区块: ${info.chunk || 'N/A'}</div>
            <div>方块: ${info.selectedBlock || 'N/A'}</div>
            <div>已加载区块: ${info.loadedChunks || 0}</div>
        `;
    }

    // 切换调试面板显示
    toggleDebugPanel() {
        const debugPanel = document.getElementById('debug-panel');
        if (debugPanel) {
            debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
        }
    }

    // 初始化游戏界面
    initGameUI() {
        this.showToolbar();
        this.showCrosshair();
        this.updateToolbar();
    }

    // 重置界面
    reset() {
        this.showLoading();
        this.showControlsInfo();
        this.hideToolbar();
        this.hideCrosshair();

        // 移除所有消息
        const messages = document.querySelectorAll('.error-message, .success-message, .info-message');
        messages.forEach(msg => {
            if (msg.parentNode) {
                msg.parentNode.removeChild(msg);
            }
        });
    }
}

// 导出到全局
window.UIManager = UIManager;
