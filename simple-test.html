<!DOCTYPE html>
<html>
<head>
    <title>简单渲染测试</title>
    <style>
        body { margin: 0; background: #000; color: white; font-family: Arial; }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>状态: <span id="status">初始化中...</span></div>
        <div>帧数: <span id="frames">0</span></div>
    </div>

    <script src="libs/three.min.js"></script>
    <script>
        let scene, camera, renderer;
        let cubes = [];
        let frameCount = 0;
        
        function updateStatus(msg) {
            document.getElementById('status').textContent = msg;
            console.log('状态:', msg);
        }
        
        function init() {
            updateStatus('创建场景...');
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 5, 10);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.body.appendChild(renderer.domElement);
            
            updateStatus('添加对象...');
            
            // 创建多个彩色立方体
            const geometry = new THREE.BoxGeometry(2, 2, 2);
            const colors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff];
            
            for (let i = 0; i < 5; i++) {
                const material = new THREE.MeshBasicMaterial({ color: colors[i] });
                const cube = new THREE.Mesh(geometry, material);
                cube.position.x = (i - 2) * 3;
                cube.position.y = 0;
                cube.position.z = 0;
                scene.add(cube);
                cubes.push(cube);
                console.log(`添加立方体 ${i}, 颜色: ${colors[i].toString(16)}`);
            }
            
            // 添加地面
            const groundGeometry = new THREE.PlaneGeometry(20, 20);
            const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -2;
            scene.add(ground);
            
            updateStatus('开始渲染...');
            
            // 开始动画循环
            animate();
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            frameCount++;
            document.getElementById('frames').textContent = frameCount;
            
            // 旋转立方体
            cubes.forEach((cube, index) => {
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                cube.position.y = Math.sin(Date.now() * 0.001 + index) * 0.5;
            });
            
            // 旋转相机
            const time = Date.now() * 0.0005;
            camera.position.x = Math.cos(time) * 15;
            camera.position.z = Math.sin(time) * 15;
            camera.lookAt(0, 0, 0);
            
            renderer.render(scene, camera);
            
            if (frameCount === 60) {
                updateStatus('渲染正常！');
            }
        }
        
        // 窗口大小改变
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            if (!window.THREE) {
                updateStatus('Three.js 未加载！');
                return;
            }
            
            updateStatus('Three.js 已加载，版本: ' + THREE.REVISION);
            
            setTimeout(init, 100);
        });
    </script>
</body>
</html>
