# 3D 我的世界风格游戏

一个基于 Web 技术的 3D 我的世界风格游戏，使用 Three.js 构建。

## 功能特性

### 核心功能
- ✅ 3D 方块世界环境
- ✅ 第一人称视角控制（WASD 移动，鼠标视角控制）
- ✅ 基础物理系统（重力、碰撞检测）
- ✅ 程序化地形生成
- ✅ 方块放置和破坏系统

### 游戏机制
- ✅ 玩家在 3D 世界中自由移动
- ✅ 左键破坏方块，右键放置方块
- ✅ 5 种不同类型的方块：
  - 🟢 草地方块
  - ⚫ 石头方块
  - 🟤 木头方块
  - 🟫 泥土方块
  - 🟡 沙子方块
- ✅ 方块选择工具栏
- ✅ 区块系统优化性能

### 技术特性
- ✅ Three.js 3D 渲染引擎
- ✅ 响应式设计
- ✅ 实例化渲染优化性能
- ✅ 面剔除优化
- ✅ 区块加载/卸载系统
- ✅ 阴影和光照效果

## 游戏控制

### 基本控制
- **WASD** - 移动
- **鼠标** - 视角控制
- **左键** - 破坏方块
- **右键** - 放置方块
- **空格** - 跳跃
- **Shift** - 奔跑
- **ESC** - 释放鼠标锁定

### 方块选择
- **数字键 1-5** - 快速选择方块类型
- **点击工具栏** - 选择方块类型

## 安装和运行

### 前置要求
- Node.js (推荐 14.0 或更高版本)
- 现代浏览器（支持 WebGL）

### 安装步骤

1. 克隆或下载项目
```bash
git clone <repository-url>
cd MC_1
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

4. 打开浏览器访问
```
http://localhost:8080
```

### 生产环境
```bash
npm start
```

## 项目结构

```
MC_1/
├── index.html          # 主 HTML 文件
├── css/
│   └── style.css       # 样式文件
├── js/
│   ├── main.js         # 主游戏逻辑
│   ├── world.js        # 世界和地形生成
│   ├── player.js       # 玩家控制和物理
│   ├── blocks.js       # 方块系统
│   ├── ui.js           # 用户界面管理
│   └── utils.js        # 工具函数
├── package.json        # 项目配置
└── README.md          # 项目说明
```

## 技术架构

### 核心系统

1. **方块系统 (blocks.js)**
   - 方块类型定义和配置
   - 方块材质管理
   - 方块选择器

2. **世界系统 (world.js)**
   - 区块生成和管理
   - 地形生成算法
   - 方块数据存储

3. **玩家系统 (player.js)**
   - 第一人称控制
   - 物理模拟（重力、碰撞）
   - 方块交互（放置/破坏）

4. **渲染系统 (main.js)**
   - Three.js 场景管理
   - 光照和阴影
   - 性能优化

5. **UI 系统 (ui.js)**
   - 用户界面管理
   - 工具栏控制
   - 消息系统

### 性能优化

- **实例化渲染**: 使用 Three.js InstancedMesh 减少绘制调用
- **面剔除**: 只渲染可见的方块面
- **区块系统**: 动态加载/卸载玩家周围的区块
- **LOD**: 距离较远的区块使用简化渲染

## 扩展功能建议

### 短期扩展
- [ ] 更多方块类型
- [ ] 简单的物品系统
- [ ] 音效和背景音乐
- [ ] 保存/加载世界

### 中期扩展
- [ ] 多人游戏支持
- [ ] 更复杂的地形生成
- [ ] 生物和 AI
- [ ] 红石系统

### 长期扩展
- [ ] 模组支持
- [ ] 自定义纹理包
- [ ] 服务器架构
- [ ] 移动端支持

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 致谢

- [Three.js](https://threejs.org/) - 3D 渲染引擎
- [Minecraft](https://minecraft.net/) - 游戏灵感来源
