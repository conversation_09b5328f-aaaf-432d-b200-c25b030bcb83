/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #000;
    overflow: hidden;
    user-select: none;
}

/* 游戏容器 */
#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

/* 游戏画布 */
#game-canvas {
    display: block;
    width: 100%;
    height: 100%;
    cursor: none;
}

/* 十字准星 */
#crosshair {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 100;
}

.crosshair-line {
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
}

.crosshair-line.horizontal {
    width: 20px;
    height: 2px;
    top: -1px;
    left: -10px;
}

.crosshair-line.vertical {
    width: 2px;
    height: 20px;
    top: -10px;
    left: -1px;
}

/* 工具栏 */
#toolbar {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 10px;
    z-index: 200;
}

.toolbar-slot {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.toolbar-slot:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.toolbar-slot.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: #fff;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.block-icon {
    width: 30px;
    height: 30px;
    border-radius: 3px;
    margin-bottom: 2px;
}

.grass-icon { background: linear-gradient(to bottom, #7CB342 0%, #4CAF50 100%); }
.stone-icon { background: linear-gradient(to bottom, #757575 0%, #424242 100%); }
.wood-icon { background: linear-gradient(to bottom, #8D6E63 0%, #5D4037 100%); }
.dirt-icon { background: linear-gradient(to bottom, #8D6E63 0%, #6D4C41 100%); }
.sand-icon { background: linear-gradient(to bottom, #FFC107 0%, #FF8F00 100%); }

.toolbar-slot span {
    font-size: 10px;
    color: white;
    text-align: center;
}

/* 控制说明 */
#controls-info {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 10px;
    max-width: 250px;
    z-index: 200;
}

#controls-info h3 {
    margin-bottom: 15px;
    color: #4CAF50;
    text-align: center;
}

#controls-info p {
    margin-bottom: 8px;
    font-size: 14px;
}

#start-game {
    width: 100%;
    padding: 10px;
    margin-top: 15px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.2s ease;
}

#start-game:hover {
    background: #45a049;
}

/* 加载提示 */
#loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loading p {
    font-size: 18px;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #toolbar {
        bottom: 10px;
        gap: 5px;
        padding: 5px;
    }
    
    .toolbar-slot {
        width: 50px;
        height: 50px;
    }
    
    .block-icon {
        width: 25px;
        height: 25px;
    }
    
    .toolbar-slot span {
        font-size: 8px;
    }
    
    #controls-info {
        top: 10px;
        right: 10px;
        padding: 15px;
        max-width: 200px;
    }
    
    #controls-info p {
        font-size: 12px;
    }
}
