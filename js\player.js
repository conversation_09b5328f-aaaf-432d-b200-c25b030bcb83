// 玩家控制和物理系统

// 玩家配置
const PLAYER_CONFIG = {
    SPEED: 5.0,           // 移动速度
    JUMP_FORCE: 8.0,      // 跳跃力度
    GRAVITY: -20.0,       // 重力
    HEIGHT: 1.8,          // 玩家高度
    WIDTH: 0.6,           // 玩家宽度
    MOUSE_SENSITIVITY: 0.003, // 鼠标灵敏度（第三人称稍微提高）
    REACH_DISTANCE: 5.0,  // 方块交互距离

    // 第三人称相机配置
    CAMERA_DISTANCE: 5.0,     // 相机距离玩家的距离
    CAMERA_HEIGHT_OFFSET: 2.0, // 相机高度偏移
    CAMERA_MIN_DISTANCE: 2.0,  // 最小相机距离
    CAMERA_MAX_DISTANCE: 10.0, // 最大相机距离
    CAMERA_COLLISION_RADIUS: 0.3, // 相机碰撞检测半径

    // 玩家模型配置
    MODEL_SCALE: 0.8,         // 玩家模型缩放
    HEAD_SIZE: 0.3,           // 头部大小
    BODY_WIDTH: 0.4,          // 身体宽度
    BODY_HEIGHT: 0.8,         // 身体高度
    ARM_LENGTH: 0.6,          // 手臂长度
    LEG_LENGTH: 0.8           // 腿部长度
};

// 玩家类
class Player {
    constructor(camera, world, scene) {
        this.camera = camera;
        this.world = world;
        this.scene = scene;

        // 位置和速度
        this.position = { x: 0, y: 50, z: 0 };
        this.velocity = { x: 0, y: 0, z: 0 };

        // 玩家朝向（独立于相机）
        this.rotation = { x: 0, y: 0 };

        // 第三人称相机控制
        this.cameraRotation = { x: -0.2, y: 0 }; // 稍微向下看
        this.cameraDistance = PLAYER_CONFIG.CAMERA_DISTANCE;
        this.cameraTarget = new THREE.Vector3();

        // 状态
        this.onGround = false;
        this.isJumping = false;

        // 控制状态
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            jump: false,
            run: false
        };

        // 鼠标控制
        this.mouseX = 0;
        this.mouseY = 0;
        this.isPointerLocked = false;

        // 射线投射器（用于方块选择）
        this.raycaster = new THREE.Raycaster();
        this.raycaster.far = PLAYER_CONFIG.REACH_DISTANCE;

        // 创建玩家模型
        this.playerModel = null;
        this.createPlayerModel();

        this.initControls();

        // 延迟初始化相机位置，确保所有组件都已创建
        setTimeout(() => {
            this.updateCameraPosition();
            console.log('相机初始位置设置完成');
        }, 100);
    }

    // 创建玩家模型
    createPlayerModel() {
        // 创建玩家模型组
        this.playerModel = new THREE.Group();

        // 材质
        const playerMaterial = new THREE.MeshLambertMaterial({ color: 0x4A90E2 });
        const skinMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB3 });

        // 头部
        const headGeometry = new THREE.BoxGeometry(
            PLAYER_CONFIG.HEAD_SIZE,
            PLAYER_CONFIG.HEAD_SIZE,
            PLAYER_CONFIG.HEAD_SIZE
        );
        this.head = new THREE.Mesh(headGeometry, skinMaterial);
        this.head.position.y = PLAYER_CONFIG.BODY_HEIGHT + PLAYER_CONFIG.HEAD_SIZE / 2;
        this.head.castShadow = true;
        this.playerModel.add(this.head);

        // 身体
        const bodyGeometry = new THREE.BoxGeometry(
            PLAYER_CONFIG.BODY_WIDTH,
            PLAYER_CONFIG.BODY_HEIGHT,
            PLAYER_CONFIG.BODY_WIDTH * 0.5
        );
        this.body = new THREE.Mesh(bodyGeometry, playerMaterial);
        this.body.position.y = PLAYER_CONFIG.BODY_HEIGHT / 2;
        this.body.castShadow = true;
        this.playerModel.add(this.body);

        // 左臂
        const armGeometry = new THREE.BoxGeometry(0.2, PLAYER_CONFIG.ARM_LENGTH, 0.2);
        this.leftArm = new THREE.Mesh(armGeometry, playerMaterial);
        this.leftArm.position.set(
            -PLAYER_CONFIG.BODY_WIDTH / 2 - 0.1,
            PLAYER_CONFIG.BODY_HEIGHT - PLAYER_CONFIG.ARM_LENGTH / 2,
            0
        );
        this.leftArm.castShadow = true;
        this.playerModel.add(this.leftArm);

        // 右臂
        this.rightArm = new THREE.Mesh(armGeometry, playerMaterial);
        this.rightArm.position.set(
            PLAYER_CONFIG.BODY_WIDTH / 2 + 0.1,
            PLAYER_CONFIG.BODY_HEIGHT - PLAYER_CONFIG.ARM_LENGTH / 2,
            0
        );
        this.rightArm.castShadow = true;
        this.playerModel.add(this.rightArm);

        // 左腿
        const legGeometry = new THREE.BoxGeometry(0.2, PLAYER_CONFIG.LEG_LENGTH, 0.2);
        this.leftLeg = new THREE.Mesh(legGeometry, playerMaterial);
        this.leftLeg.position.set(
            -PLAYER_CONFIG.BODY_WIDTH / 4,
            -PLAYER_CONFIG.LEG_LENGTH / 2,
            0
        );
        this.leftLeg.castShadow = true;
        this.playerModel.add(this.leftLeg);

        // 右腿
        this.rightLeg = new THREE.Mesh(legGeometry, playerMaterial);
        this.rightLeg.position.set(
            PLAYER_CONFIG.BODY_WIDTH / 4,
            -PLAYER_CONFIG.LEG_LENGTH / 2,
            0
        );
        this.rightLeg.castShadow = true;
        this.playerModel.add(this.rightLeg);

        // 缩放整个模型
        this.playerModel.scale.setScalar(PLAYER_CONFIG.MODEL_SCALE);

        // 添加到场景
        if (this.scene) {
            this.scene.add(this.playerModel);
            console.log('玩家模型已添加到场景');
        } else {
            console.error('场景对象不存在，无法添加玩家模型');
        }

        console.log('玩家模型创建完成，组件数量:', this.playerModel.children.length);
    }

    // 初始化控制
    initControls() {
        // 键盘事件
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));

        // 鼠标事件
        document.addEventListener('mousemove', (event) => this.onMouseMove(event));
        document.addEventListener('mousedown', (event) => this.onMouseDown(event));
        document.addEventListener('mouseup', (event) => this.onMouseUp(event));
        document.addEventListener('wheel', (event) => this.onMouseWheel(event), { passive: false });

        // 指针锁定事件
        document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
        document.addEventListener('pointerlockerror', () => this.onPointerLockError());
    }

    // 键盘按下事件
    onKeyDown(event) {
        switch (event.code) {
            case 'KeyW':
                this.keys.forward = true;
                break;
            case 'KeyS':
                this.keys.backward = true;
                break;
            case 'KeyA':
                this.keys.left = true;
                break;
            case 'KeyD':
                this.keys.right = true;
                break;
            case 'Space':
                this.keys.jump = true;
                event.preventDefault();
                break;
            case 'ShiftLeft':
                this.keys.run = true;
                break;
            case 'Escape':
                this.exitPointerLock();
                break;
            case 'Digit1':
                window.gameInstance?.blockSelector?.selectByIndex(0);
                window.gameInstance?.ui?.updateToolbar();
                break;
            case 'Digit2':
                window.gameInstance?.blockSelector?.selectByIndex(1);
                window.gameInstance?.ui?.updateToolbar();
                break;
            case 'Digit3':
                window.gameInstance?.blockSelector?.selectByIndex(2);
                window.gameInstance?.ui?.updateToolbar();
                break;
            case 'Digit4':
                window.gameInstance?.blockSelector?.selectByIndex(3);
                window.gameInstance?.ui?.updateToolbar();
                break;
            case 'Digit5':
                window.gameInstance?.blockSelector?.selectByIndex(4);
                window.gameInstance?.ui?.updateToolbar();
                break;
        }
    }

    // 键盘释放事件
    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW':
                this.keys.forward = false;
                break;
            case 'KeyS':
                this.keys.backward = false;
                break;
            case 'KeyA':
                this.keys.left = false;
                break;
            case 'KeyD':
                this.keys.right = false;
                break;
            case 'Space':
                this.keys.jump = false;
                break;
            case 'ShiftLeft':
                this.keys.run = false;
                break;
        }
    }

    // 鼠标移动事件
    onMouseMove(event) {
        if (!this.isPointerLocked) return;

        const movementX = event.movementX || event.mozMovementX || event.webkitMovementX || 0;
        const movementY = event.movementY || event.mozMovementY || event.webkitMovementY || 0;

        // 第三人称相机控制
        this.cameraRotation.y -= movementX * PLAYER_CONFIG.MOUSE_SENSITIVITY;
        this.cameraRotation.x -= movementY * PLAYER_CONFIG.MOUSE_SENSITIVITY;

        // 限制垂直旋转角度
        this.cameraRotation.x = GameUtils.clamp(this.cameraRotation.x, -Math.PI / 3, Math.PI / 3);

        this.updateCameraPosition();
    }

    // 鼠标按下事件
    onMouseDown(event) {
        if (!this.isPointerLocked) {
            this.requestPointerLock();
            return;
        }

        event.preventDefault();

        if (event.button === 0) {
            // 左键 - 破坏方块
            this.destroyBlock();
        } else if (event.button === 2) {
            // 右键 - 放置方块
            this.placeBlock();
        }
    }

    // 鼠标释放事件
    onMouseUp(event) {
        // 可以在这里添加鼠标释放的逻辑
    }

    // 鼠标滚轮事件（调整相机距离）
    onMouseWheel(event) {
        if (!this.isPointerLocked) return;

        event.preventDefault();

        const delta = event.deltaY > 0 ? 1 : -1;
        this.cameraDistance += delta * 0.5;

        // 限制相机距离范围
        this.cameraDistance = GameUtils.clamp(
            this.cameraDistance,
            PLAYER_CONFIG.CAMERA_MIN_DISTANCE,
            PLAYER_CONFIG.CAMERA_MAX_DISTANCE
        );

        this.updateCameraPosition();
    }

    // 请求指针锁定
    requestPointerLock() {
        const canvas = document.getElementById('game-canvas');
        canvas.requestPointerLock = canvas.requestPointerLock ||
                                   canvas.mozRequestPointerLock ||
                                   canvas.webkitRequestPointerLock;
        canvas.requestPointerLock();
    }

    // 退出指针锁定
    exitPointerLock() {
        document.exitPointerLock = document.exitPointerLock ||
                                  document.mozExitPointerLock ||
                                  document.webkitExitPointerLock;
        document.exitPointerLock();
    }

    // 指针锁定状态改变
    onPointerLockChange() {
        this.isPointerLocked = document.pointerLockElement === document.getElementById('game-canvas') ||
                              document.mozPointerLockElement === document.getElementById('game-canvas') ||
                              document.webkitPointerLockElement === document.getElementById('game-canvas');

        if (this.isPointerLocked) {
            document.getElementById('controls-info').style.display = 'none';
        } else {
            document.getElementById('controls-info').style.display = 'block';
        }
    }

    // 指针锁定错误
    onPointerLockError() {
        console.error('指针锁定失败');
    }

    // 更新玩家状态
    update(deltaTime) {
        this.updateMovement(deltaTime);
        this.updatePhysics(deltaTime);
        this.updateCameraPosition();

        // 更新世界区块
        this.world.updateChunks(this.position.x, this.position.z);
    }

    // 更新移动
    updateMovement(deltaTime) {
        if (!this.isPointerLocked) return;

        const speed = this.keys.run ? PLAYER_CONFIG.SPEED * 1.5 : PLAYER_CONFIG.SPEED;
        const moveVector = { x: 0, z: 0 };

        // 基于相机方向计算移动方向（第三人称）
        const cameraYaw = this.cameraRotation.y;

        if (this.keys.forward) {
            moveVector.x += Math.sin(cameraYaw);
            moveVector.z += Math.cos(cameraYaw);
        }
        if (this.keys.backward) {
            moveVector.x -= Math.sin(cameraYaw);
            moveVector.z -= Math.cos(cameraYaw);
        }
        if (this.keys.left) {
            moveVector.x += Math.cos(cameraYaw);
            moveVector.z -= Math.sin(cameraYaw);
        }
        if (this.keys.right) {
            moveVector.x -= Math.cos(cameraYaw);
            moveVector.z += Math.sin(cameraYaw);
        }

        // 归一化移动向量
        const length = Math.sqrt(moveVector.x * moveVector.x + moveVector.z * moveVector.z);
        if (length > 0) {
            moveVector.x = (moveVector.x / length) * speed * deltaTime;
            moveVector.z = (moveVector.z / length) * speed * deltaTime;

            // 更新玩家朝向（朝向移动方向）
            this.rotation.y = Math.atan2(-moveVector.x, -moveVector.z);
        }

        // 应用移动（带碰撞检测）
        this.moveWithCollision(moveVector.x, 0, moveVector.z);

        // 跳跃
        if (this.keys.jump && this.onGround && !this.isJumping) {
            this.velocity.y = PLAYER_CONFIG.JUMP_FORCE;
            this.isJumping = true;
            this.onGround = false;
        }

        // 更新玩家模型动画
        this.updatePlayerAnimation(length > 0);
    }

    // 带碰撞检测的移动
    moveWithCollision(dx, dy, dz) {
        // X轴移动
        const newX = this.position.x + dx;
        if (!this.checkCollision(newX, this.position.y, this.position.z)) {
            this.position.x = newX;
        }

        // Y轴移动
        const newY = this.position.y + dy;
        if (!this.checkCollision(this.position.x, newY, this.position.z)) {
            this.position.y = newY;
        } else {
            this.velocity.y = 0;
            if (dy < 0) {
                this.onGround = true;
                this.isJumping = false;
            }
        }

        // Z轴移动
        const newZ = this.position.z + dz;
        if (!this.checkCollision(this.position.x, this.position.y, newZ)) {
            this.position.z = newZ;
        }
    }

    // 碰撞检测
    checkCollision(x, y, z) {
        const halfWidth = PLAYER_CONFIG.WIDTH / 2;
        const height = PLAYER_CONFIG.HEIGHT;

        // 检查玩家包围盒与方块的碰撞
        const minX = Math.floor(x - halfWidth);
        const maxX = Math.floor(x + halfWidth);
        const minY = Math.floor(y);
        const maxY = Math.floor(y + height);
        const minZ = Math.floor(z - halfWidth);
        const maxZ = Math.floor(z + halfWidth);

        for (let bx = minX; bx <= maxX; bx++) {
            for (let by = minY; by <= maxY; by++) {
                for (let bz = minZ; bz <= maxZ; bz++) {
                    if (this.world.isSolid(bx, by, bz)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // 更新物理
    updatePhysics(deltaTime) {
        // 应用重力
        this.velocity.y += PLAYER_CONFIG.GRAVITY * deltaTime;

        // 应用Y轴速度
        this.moveWithCollision(0, this.velocity.y * deltaTime, 0);

        // 检查是否在地面上
        if (this.onGround) {
            this.velocity.y = 0;
        }
    }

    // 更新相机位置（第三人称）
    updateCameraPosition() {
        // 更新玩家模型位置和旋转
        if (this.playerModel) {
            this.playerModel.position.set(this.position.x, this.position.y, this.position.z);
            this.playerModel.rotation.y = this.rotation.y;
        }

        // 计算相机目标位置（玩家位置 + 高度偏移）
        this.cameraTarget.set(
            this.position.x,
            this.position.y + PLAYER_CONFIG.CAMERA_HEIGHT_OFFSET,
            this.position.z
        );

        // 计算相机位置（基于距离和角度）
        const distance = this.cameraDistance;
        const offsetX = Math.sin(this.cameraRotation.y) * Math.cos(this.cameraRotation.x) * distance;
        const offsetY = Math.sin(this.cameraRotation.x) * distance;
        const offsetZ = Math.cos(this.cameraRotation.y) * Math.cos(this.cameraRotation.x) * distance;

        const idealCameraPos = new THREE.Vector3(
            this.cameraTarget.x + offsetX,
            this.cameraTarget.y + offsetY,
            this.cameraTarget.z + offsetZ
        );

        // 安全检查：确保相机位置是有效的
        if (isNaN(idealCameraPos.x) || isNaN(idealCameraPos.y) || isNaN(idealCameraPos.z)) {
            console.error('相机位置计算错误:', idealCameraPos);
            // 使用默认位置
            idealCameraPos.set(
                this.position.x,
                this.position.y + 3,
                this.position.z + 5
            );
        }

        // 相机碰撞检测
        const finalCameraPos = this.checkCameraCollision(this.cameraTarget, idealCameraPos);

        // 设置相机位置和朝向
        this.camera.position.copy(finalCameraPos);
        this.camera.lookAt(this.cameraTarget);

        // 调试信息（每300帧输出一次，减少控制台输出）
        if (this.debugFrameCount === undefined) this.debugFrameCount = 0;
        this.debugFrameCount++;
        if (this.debugFrameCount % 300 === 0) {
            console.log('第三人称相机状态 - 位置:', this.camera.position, '目标:', this.cameraTarget);
        }
    }

    // 相机碰撞检测
    checkCameraCollision(target, idealPos) {
        // 暂时简化碰撞检测，直接返回理想位置
        // 后续可以添加更复杂的碰撞检测
        try {
            const direction = idealPos.clone().sub(target).normalize();
            const distance = target.distanceTo(idealPos);
            const step = 0.5;

            // 从目标位置向理想位置检测
            for (let d = PLAYER_CONFIG.CAMERA_MIN_DISTANCE; d <= distance; d += step) {
                const testPos = target.clone().add(direction.clone().multiplyScalar(d));

                // 检查是否与方块碰撞
                if (this.world && this.world.isSolid && this.world.isSolid(
                    Math.floor(testPos.x),
                    Math.floor(testPos.y),
                    Math.floor(testPos.z)
                )) {
                    // 返回碰撞前的位置
                    return target.clone().add(direction.clone().multiplyScalar(Math.max(d - step, PLAYER_CONFIG.CAMERA_MIN_DISTANCE)));
                }
            }
        } catch (error) {
            console.warn('相机碰撞检测错误:', error);
        }

        return idealPos;
    }

    // 更新玩家动画
    updatePlayerAnimation(isMoving) {
        if (!this.playerModel) return;

        const time = Date.now() * 0.005;

        if (isMoving) {
            // 行走动画 - 摆动手臂和腿部
            const swingAmount = 0.3;
            const swingSpeed = 8;

            this.leftArm.rotation.x = Math.sin(time * swingSpeed) * swingAmount;
            this.rightArm.rotation.x = -Math.sin(time * swingSpeed) * swingAmount;
            this.leftLeg.rotation.x = -Math.sin(time * swingSpeed) * swingAmount;
            this.rightLeg.rotation.x = Math.sin(time * swingSpeed) * swingAmount;

            // 轻微的身体摆动
            this.body.rotation.z = Math.sin(time * swingSpeed * 0.5) * 0.05;
        } else {
            // 静止状态 - 重置动画
            this.leftArm.rotation.x = 0;
            this.rightArm.rotation.x = 0;
            this.leftLeg.rotation.x = 0;
            this.rightLeg.rotation.x = 0;
            this.body.rotation.z = 0;
        }

        // 跳跃动画
        if (this.isJumping) {
            this.leftArm.rotation.x = -0.5;
            this.rightArm.rotation.x = -0.5;
            this.leftLeg.rotation.x = 0.3;
            this.rightLeg.rotation.x = 0.3;
        }
    }

    // 获取玩家看向的方块（第三人称）
    getTargetBlock() {
        // 从相机位置向相机目标方向发射射线
        const direction = this.cameraTarget.clone().sub(this.camera.position).normalize();

        this.raycaster.set(this.camera.position, direction);

        // 遍历射线路径上的方块
        const step = 0.1;
        const maxDistance = PLAYER_CONFIG.REACH_DISTANCE;

        for (let distance = 0; distance < maxDistance; distance += step) {
            const point = this.camera.position.clone().add(direction.clone().multiplyScalar(distance));
            const blockX = Math.floor(point.x);
            const blockY = Math.floor(point.y);
            const blockZ = Math.floor(point.z);

            if (this.world.isSolid(blockX, blockY, blockZ)) {
                return {
                    position: { x: blockX, y: blockY, z: blockZ },
                    distance: distance
                };
            }
        }

        return null;
    }

    // 获取放置方块的位置（第三人称）
    getPlacePosition() {
        const target = this.getTargetBlock();
        if (!target) return null;

        // 计算放置位置（目标方块的相邻位置）
        const direction = this.cameraTarget.clone().sub(this.camera.position).normalize();

        const step = 0.05;
        const targetDistance = target.distance;

        // 从目标方块向后查找第一个空气方块
        for (let distance = targetDistance - step; distance > 0; distance -= step) {
            const point = this.camera.position.clone().add(direction.clone().multiplyScalar(distance));
            const blockX = Math.floor(point.x);
            const blockY = Math.floor(point.y);
            const blockZ = Math.floor(point.z);

            if (!this.world.isSolid(blockX, blockY, blockZ)) {
                // 确保不会放置在玩家位置
                if (!this.checkCollision(blockX + 0.5, this.position.y, blockZ + 0.5)) {
                    return { x: blockX, y: blockY, z: blockZ };
                }
            }
        }

        return null;
    }

    // 破坏方块
    destroyBlock() {
        const target = this.getTargetBlock();
        if (target) {
            this.world.setBlock(target.position.x, target.position.y, target.position.z, BLOCK_TYPES.AIR);
        }
    }

    // 放置方块
    placeBlock() {
        const placePos = this.getPlacePosition();
        if (placePos && window.gameInstance?.blockSelector) {
            const selectedBlock = window.gameInstance.blockSelector.getSelectedBlock();
            this.world.setBlock(placePos.x, placePos.y, placePos.z, selectedBlock);
        }
    }

    // 设置玩家位置
    setPosition(x, y, z) {
        this.position.x = x;
        this.position.y = y;
        this.position.z = z;
        this.updateCameraPosition();
    }

    // 获取玩家位置
    getPosition() {
        return { ...this.position };
    }

    // 重置玩家到安全位置
    respawn() {
        const groundHeight = this.world.getGroundHeight(0, 0);
        this.setPosition(0, groundHeight + 2, 0);
        this.velocity = { x: 0, y: 0, z: 0 };
        this.onGround = false;
        this.isJumping = false;
    }
}

// 导出到全局
window.PLAYER_CONFIG = PLAYER_CONFIG;
window.Player = Player;
